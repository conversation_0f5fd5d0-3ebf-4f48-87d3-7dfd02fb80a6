// NoSQL Booster Queries - MongoDB queries formatted for NoSQL Booster
// Copy and paste these queries into NoSQL Booster's query editor

// ========================================
// SETUP: Make sure you're connected to plp_bookstore database
// ========================================

// First, populate your database by running insert_books.js
// Then use these queries in NoSQL Booster

// ========================================
// TASK 2: BASIC CRUD OPERATIONS
// ========================================

// 1. Find all books in Fiction genre
db.books.find({ genre: "Fiction" });

// 2. Find books published after 1950
db.books.find({ published_year: { $gt: 1950 } });

// 3. Find books by <PERSON>
db.books.find({ author: "<PERSON>" });

// 4. Update price of The Great Gatsby
db.books.updateOne(
  { title: "The Great Gatsby" },
  { $set: { price: 15.99 } }
);

// Verify the update
db.books.findOne({ title: "The Great Gatsby" });

// 5. Delete Animal Farm (be careful with this!)
db.books.deleteOne({ title: "Animal Farm" });

// ========================================
// TASK 3: ADVANCED QUERIES
// ========================================

// 1. Books in stock AND published after 2010
db.books.find({
  $and: [
    { in_stock: true },
    { published_year: { $gt: 2010 } }
  ]
});

// 2. Projection - only title, author, price
db.books.find(
  {},
  { title: 1, author: 1, price: 1, _id: 0 }
);

// 3. Sort by price ascending
db.books.find({}, { title: 1, price: 1, _id: 0 }).sort({ price: 1 });

// 4. Sort by price descending
db.books.find({}, { title: 1, price: 1, _id: 0 }).sort({ price: -1 });

// 5. Pagination - First 5 books
db.books.find({}, { title: 1, author: 1, price: 1, _id: 0 }).limit(5);

// 6. Pagination - Next 5 books (skip first 5)
db.books.find({}, { title: 1, author: 1, price: 1, _id: 0 }).skip(5).limit(5);

// ========================================
// TASK 4: AGGREGATION PIPELINES
// ========================================

// 1. Average price by genre
db.books.aggregate([
  {
    $group: {
      _id: "$genre",
      averagePrice: { $avg: "$price" },
      bookCount: { $sum: 1 }
    }
  },
  {
    $sort: { averagePrice: -1 }
  }
]);

// 2. Author with most books
db.books.aggregate([
  {
    $group: {
      _id: "$author",
      bookCount: { $sum: 1 },
      books: { $push: "$title" }
    }
  },
  {
    $sort: { bookCount: -1 }
  },
  {
    $limit: 1
  }
]);

// 3. Books by publication decade
db.books.aggregate([
  {
    $addFields: {
      decade: {
        $multiply: [
          { $floor: { $divide: ["$published_year", 10] } },
          10
        ]
      }
    }
  },
  {
    $group: {
      _id: "$decade",
      bookCount: { $sum: 1 },
      books: { $push: { title: "$title", year: "$published_year" } }
    }
  },
  {
    $sort: { _id: 1 }
  }
]);

// ========================================
// TASK 5: INDEXING
// ========================================

// 1. Create index on title
db.books.createIndex({ title: 1 });

// 2. Create compound index on author and published_year
db.books.createIndex({ author: 1, published_year: 1 });

// 3. List all indexes
db.books.getIndexes();

// 4. Query performance with explain
db.books.find({ title: "1984" }).explain("executionStats");

// 5. Compound index performance
db.books.find({ 
  author: "George Orwell", 
  published_year: { $gte: 1940 } 
}).explain("executionStats");

// ========================================
// ADDITIONAL USEFUL QUERIES
// ========================================

// Count total books
db.books.countDocuments();

// Books in price range $10-$15
db.books.find({
  price: { $gte: 10, $lte: 15 }
}, { title: 1, price: 1, _id: 0 });

// Books with more than 300 pages
db.books.find({
  pages: { $gt: 300 }
}, { title: 1, pages: 1, _id: 0 }).sort({ pages: -1 });

// Out of stock books
db.books.find({ in_stock: false }, { title: 1, author: 1, _id: 0 });

// Books by specific genres
db.books.find({ genre: { $in: ["Fiction", "Fantasy"] } });

// Books published in a specific decade (1940s)
db.books.find({
  published_year: { $gte: 1940, $lt: 1950 }
});

// Most expensive books (top 3)
db.books.find({}, { title: 1, price: 1, _id: 0 }).sort({ price: -1 }).limit(3);

// Books with title containing specific word
db.books.find({ title: { $regex: "The", $options: "i" } });

// Update multiple books (increase all prices by 10%)
db.books.updateMany(
  {},
  { $mul: { price: 1.1 } }
);

// Find books and group by availability
db.books.aggregate([
  {
    $group: {
      _id: "$in_stock",
      count: { $sum: 1 },
      books: { $push: "$title" }
    }
  }
]);
