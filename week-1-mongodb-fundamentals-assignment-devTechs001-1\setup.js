// setup.js - Quick setup script to verify MongoDB connection and create database
const { MongoClient } = require('mongodb');

// Connection URI - modify this if using Atlas
const uri = 'mongodb://localhost:27017';
const dbName = 'plp_bookstore';

async function setupDatabase() {
  console.log('🚀 Setting up MongoDB database...\n');
  
  const client = new MongoClient(uri);
  
  try {
    // Test connection
    console.log('📡 Connecting to MongoDB...');
    await client.connect();
    console.log('✅ Successfully connected to MongoDB!\n');
    
    // Create database and collection
    const db = client.db(dbName);
    const collection = db.collection('books');
    
    // Check if database exists
    const admin = db.admin();
    const databases = await admin.listDatabases();
    const dbExists = databases.databases.some(database => database.name === dbName);
    
    if (dbExists) {
      console.log(`📚 Database '${dbName}' already exists`);
      const count = await collection.countDocuments();
      console.log(`📖 Books collection contains ${count} documents\n`);
    } else {
      console.log(`📚 Creating database '${dbName}'...`);
      // Create a dummy document to initialize the database
      await collection.insertOne({ _temp: true });
      await collection.deleteOne({ _temp: true });
      console.log('✅ Database created successfully!\n');
    }
    
    // Display connection info
    console.log('🔧 Database Configuration:');
    console.log(`   Database Name: ${dbName}`);
    console.log(`   Collection: books`);
    console.log(`   Connection URI: ${uri}\n`);
    
    // List all databases
    console.log('📋 Available databases:');
    databases.databases.forEach(db => {
      console.log(`   - ${db.name}`);
    });
    
    console.log('\n🎉 Setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run: node insert_books.js (to populate with sample data)');
    console.log('   2. Run: mongosh < queries.js (to execute queries)');
    console.log('   3. Or use MongoDB Compass for GUI interaction');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure MongoDB is running:');
    console.log('      Windows: net start MongoDB');
    console.log('      macOS: brew services start mongodb/brew/mongodb-community');
    console.log('      Linux: sudo systemctl start mongod');
    console.log('   2. Check if MongoDB is installed correctly');
    console.log('   3. Verify the connection URI is correct');
    console.log('   4. For Atlas: Update the URI with your cluster connection string');
  } finally {
    await client.close();
    console.log('\n🔌 Connection closed');
  }
}

// Run setup
setupDatabase().catch(console.error);
