# 🚀 NoSQL Booster Setup and Usage Guide

## What is NoSQL Booster?
NoSQL Booster is a cross-platform MongoDB GUI tool that provides:
- Visual query builder
- IntelliSense code completion
- Query result visualization
- Schema analyzer
- Performance monitoring

## 📥 Installation

### Step 1: Download NoSQL Booster
1. Go to [https://nosqlbooster.com/](https://nosqlbooster.com/)
2. Download the version for your operating system
3. Install and launch the application

### Step 2: Connect to MongoDB

#### For Local MongoDB:
1. Click **"Connect"** or the **"+"** button
2. **Connection Settings**:
   - **Connection Name**: `PLP Bookstore Local`
   - **Host**: `localhost`
   - **Port**: `27017`
   - **Authentication**: None (for local setup)
3. Click **"Test"** to verify connection
4. Click **"Save & Connect"**

#### For MongoDB Atlas:
1. Click **"Connect"** or the **"+"** button
2. **Connection Settings**:
   - **Connection Name**: `PLP Bookstore Atlas`
   - **Connection String**: Paste your Atlas connection string
   - Example: `mongodb+srv://username:<EMAIL>/`
3. Click **"Test"** to verify connection
4. Click **"Save & Connect"**

## 🗄️ Setting Up Your Database

### Step 1: Create Database Structure
1. **Right-click** on your connection
2. Select **"Create Database"**
3. **Database Name**: `plp_bookstore`
4. **Collection Name**: `books`
5. Click **"Create"**

### Step 2: Populate Database
You have two options:

#### Option A: Run insert_books.js from Terminal
```bash
# In your project directory
node insert_books.js
```

#### Option B: Insert Data Directly in NoSQL Booster
1. Select `plp_bookstore` database
2. Right-click on `books` collection
3. Select **"Insert Document"**
4. Copy the books array from `insert_books.js` and paste
5. Click **"Insert"**

## 🔍 Running Queries in NoSQL Booster

### Method 1: Query Tab
1. Click **"Query"** tab at the bottom
2. Make sure you're connected to `plp_bookstore` database
3. Copy queries from `nosql-booster-queries.js`
4. Paste into the query editor
5. Click **"Run"** (F5) or the play button

### Method 2: IntelliShell
1. Click **"IntelliShell"** tab
2. Type your queries with auto-completion
3. Press **Ctrl+Enter** to execute

## 📊 Query Examples for NoSQL Booster

### Basic Queries
```javascript
// Select database first
use plp_bookstore

// Find all books
db.books.find()

// Find fiction books
db.books.find({ genre: "Fiction" })

// Count documents
db.books.countDocuments()
```

### Advanced Queries with Visualization
```javascript
// Aggregation with chart visualization
db.books.aggregate([
  {
    $group: {
      _id: "$genre",
      count: { $sum: 1 },
      avgPrice: { $avg: "$price" }
    }
  },
  { $sort: { count: -1 } }
])
```

## 🎨 NoSQL Booster Features

### 1. Visual Query Builder
- Click **"Visual Query"** tab
- Drag and drop fields to build queries
- Great for beginners

### 2. Schema Analyzer
- Right-click collection → **"Schema"**
- View field types and distributions
- Identify data patterns

### 3. Query Performance
- Use `.explain()` to analyze query performance
- View execution statistics
- Optimize slow queries

### 4. Data Visualization
- Aggregation results can be visualized as charts
- Click **"Chart"** tab after running aggregation
- Choose chart type (bar, pie, line, etc.)

### 5. Export/Import
- Export query results to JSON, CSV, Excel
- Import data from various formats
- Backup and restore collections

## 🔧 Useful NoSQL Booster Shortcuts

| Shortcut | Action |
|----------|--------|
| `F5` | Run query |
| `Ctrl+Enter` | Execute current line |
| `Ctrl+/` | Comment/uncomment |
| `Ctrl+D` | Duplicate line |
| `Ctrl+Shift+F` | Format query |
| `F1` | Help |

## 📝 Step-by-Step Workflow

### 1. Initial Setup
```javascript
// 1. Connect to database
use plp_bookstore

// 2. Verify data exists
db.books.countDocuments()

// 3. View sample document
db.books.findOne()
```

### 2. Run Assignment Queries
Copy queries from `nosql-booster-queries.js` one by one:

```javascript
// Example: Find fiction books
db.books.find({ genre: "Fiction" })
```

### 3. Create Indexes
```javascript
// Create indexes for performance
db.books.createIndex({ title: 1 })
db.books.createIndex({ author: 1, published_year: 1 })
```

### 4. Test Performance
```javascript
// Check query performance
db.books.find({ title: "1984" }).explain("executionStats")
```

## 🎯 Tips for Success

### 1. Use IntelliSense
- NoSQL Booster provides auto-completion
- Type `db.books.` and see available methods
- Use `Ctrl+Space` for suggestions

### 2. Save Frequently Used Queries
- Right-click in query editor
- Select **"Save Query"**
- Organize in folders

### 3. Use Result Tabs
- Each query opens in a new result tab
- Compare results side by side
- Export results easily

### 4. Monitor Performance
- Use **"Profiler"** to monitor slow queries
- Check **"Server Status"** for database health
- Use **"Real-time Performance"** monitoring

## 🚨 Common Issues and Solutions

### Connection Issues
- **Problem**: Can't connect to MongoDB
- **Solution**: 
  - Ensure MongoDB service is running
  - Check firewall settings
  - Verify connection string

### Query Errors
- **Problem**: Syntax errors in queries
- **Solution**:
  - Use IntelliSense for correct syntax
  - Check MongoDB documentation
  - Start with simple queries

### Performance Issues
- **Problem**: Slow query execution
- **Solution**:
  - Create appropriate indexes
  - Use `.explain()` to analyze
  - Optimize query structure

## 📚 Next Steps

1. **Run all assignment queries** from `nosql-booster-queries.js`
2. **Create visualizations** for aggregation results
3. **Export results** for documentation
4. **Experiment** with the visual query builder
5. **Save your queries** for future reference

## 🔗 Resources

- [NoSQL Booster Documentation](https://nosqlbooster.com/features)
- [MongoDB Query Reference](https://docs.mongodb.com/manual/reference/operator/query/)
- [Aggregation Pipeline](https://docs.mongodb.com/manual/core/aggregation-pipeline/)

---

**Pro Tip**: NoSQL Booster's IntelliSense and visual features make it perfect for learning MongoDB. Use it alongside the command line to get the best of both worlds!
