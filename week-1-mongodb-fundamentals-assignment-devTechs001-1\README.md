# 🗄️ MongoDB Fundamentals Assignment - PLP Bookstore

[![Open in Visual Studio Code](https://classroom.github.com/assets/open-in-vscode-2e0aaae1b6195c2367325f4f02e2d04e9abb55f0b24a779b69b11b9e10269abc.svg)](https://classroom.github.com/online_ide?assignment_repo_id=19724852&assignment_repo_type=AssignmentRepo)

This assignment demonstrates MongoDB fundamentals including database setup, CRUD operations, advanced queries, aggregation pipelines, and indexing optimization.

## 📋 Assignment Overview

This project implements a complete MongoDB bookstore database with:
1. **Database Setup**: MongoDB database with book collection
2. **CRUD Operations**: Create, Read, Update, Delete operations
3. **Advanced Queries**: Filtering, projection, sorting, and pagination
4. **Aggregation Pipelines**: Data analysis and transformation
5. **Indexing**: Performance optimization with single and compound indexes

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (local installation or Atlas account)
- MongoDB Shell (mongosh) or MongoDB Compass

### Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd week-1-mongodb-fundamentals-assignment-devTechs001-1
   ```

2. **Install dependencies**
   ```bash
   npm init -y
   npm install mongodb
   ```

3. **Start MongoDB** (if using local installation)
   ```bash
   # On Windows
   net start MongoDB

   # On macOS/Linux
   sudo systemctl start mongod
   ```

4. **Populate the database**
   ```bash
   node insert_books.js
   ```

5. **Run queries**
   ```bash
   # Using MongoDB Shell
   mongosh < queries.js

   # Or connect to MongoDB Shell and copy-paste queries
   mongosh
   ```

## 📁 Project Structure

```
week-1-mongodb-fundamentals-assignment-devTechs001-1/
├── README.md                 # This file
├── Week1-Assignment.md       # Detailed assignment instructions
├── insert_books.js          # Script to populate database with sample data
├── queries.js              # All MongoDB queries for the assignment
└── package.json            # Node.js dependencies (after npm init)
```

## 🗄️ Database Schema

**Database**: `plp_bookstore`
**Collection**: `books`

**Book Document Structure**:
```javascript
{
  title: String,           // Book title
  author: String,          // Author name
  genre: String,           // Book genre
  published_year: Number,  // Publication year
  price: Number,           // Book price
  in_stock: Boolean,       // Availability status
  pages: Number,           // Number of pages
  publisher: String        // Publisher name
}
```

## 📊 Sample Data

The database includes 12 classic books:
- To Kill a Mockingbird (Harper Lee)
- 1984 (George Orwell)
- The Great Gatsby (F. Scott Fitzgerald)
- Brave New World (Aldous Huxley)
- The Hobbit (J.R.R. Tolkien)
- And more...

## 🔍 Implemented Queries

### Basic CRUD Operations
- ✅ Find books by genre
- ✅ Find books published after specific year
- ✅ Find books by author
- ✅ Update book price
- ✅ Delete book by title

### Advanced Queries
- ✅ Complex filtering (in stock AND published after year)
- ✅ Field projection (title, author, price only)
- ✅ Sorting (ascending/descending by price)
- ✅ Pagination (limit and skip)

### Aggregation Pipelines
- ✅ Average price by genre
- ✅ Author with most books
- ✅ Books grouped by publication decade

### Indexing
- ✅ Single field index on `title`
- ✅ Compound index on `author` and `published_year`
- ✅ Performance analysis with `explain()`

## 🛠️ Usage Examples

### Running Individual Queries

```javascript
// Connect to database
use plp_bookstore

// Find all fiction books
db.books.find({ genre: "Fiction" })

// Find books with pagination
db.books.find().limit(5).skip(0)

// Aggregation example
db.books.aggregate([
  { $group: { _id: "$genre", avgPrice: { $avg: "$price" } } }
])
```

### Performance Testing

```javascript
// Create indexes
db.books.createIndex({ title: 1 })
db.books.createIndex({ author: 1, published_year: 1 })

// Test query performance
db.books.find({ title: "1984" }).explain("executionStats")
```

## 📈 Performance Optimization

The project implements several indexing strategies:

1. **Single Field Index**: `title` field for fast title searches
2. **Compound Index**: `author` + `published_year` for complex queries
3. **Query Optimization**: Using `explain()` to analyze performance

## 🧪 Testing

To verify all queries work correctly:

1. Run the insert script: `node insert_books.js`
2. Execute queries in MongoDB Shell: `mongosh < queries.js`
3. Check results in MongoDB Compass for visual verification

## 📝 Assignment Completion

- [x] **Task 1**: MongoDB Setup (database and collection created)
- [x] **Task 2**: Basic CRUD Operations (all 5 operations implemented)
- [x] **Task 3**: Advanced Queries (filtering, projection, sorting, pagination)
- [x] **Task 4**: Aggregation Pipeline (3 different pipelines)
- [x] **Task 5**: Indexing (single and compound indexes with performance analysis)

## 🔗 Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [MongoDB University](https://university.mongodb.com/)
- [MongoDB Node.js Driver](https://mongodb.github.io/node-mongodb-native/)
- [MongoDB Aggregation Pipeline](https://docs.mongodb.com/manual/core/aggregation-pipeline/)

## 📧 Contact

For questions about this assignment, please refer to the course materials or contact your instructor.

---
**Note**: This project is part of the PLP (Power Learn Project) MEARN Stack curriculum, Week 1 MongoDB Fundamentals assignment.