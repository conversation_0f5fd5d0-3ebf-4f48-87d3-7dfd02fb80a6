// queries.js - MongoDB queries for the PLP Bookstore assignment
// Run these queries in MongoDB Shell (mongosh) or MongoDB Compass

// Connect to the database
use plp_bookstore

// ========================================
// TASK 2: BASIC CRUD OPERATIONS
// ========================================

// 1. Find all books in a specific genre (Fiction)
console.log("=== Books in Fiction genre ===");
db.books.find({ genre: "Fiction" }).pretty();

// 2. Find books published after a certain year (after 1950)
console.log("=== Books published after 1950 ===");
db.books.find({ published_year: { $gt: 1950 } }).pretty();

// 3. Find books by a specific author (<PERSON>)
console.log("=== Books by <PERSON> ===");
db.books.find({ author: "<PERSON>" }).pretty();

// 4. Update the price of a specific book (The Great Gatsby)
console.log("=== Updating price of The Great Gatsby ===");
db.books.updateOne(
  { title: "The Great Gatsby" },
  { $set: { price: 15.99 } }
);

// Verify the update
db.books.findOne({ title: "The Great Gatsby" });

// 5. Delete a book by its title (example: delete Animal Farm)
console.log("=== Deleting Animal Farm ===");
db.books.deleteOne({ title: "Animal Farm" });

// Verify deletion
db.books.find({ title: "Animal Farm" });

// ========================================
// TASK 3: ADVANCED QUERIES
// ========================================

// 1. Find books that are both in stock and published after 2010
console.log("=== Books in stock and published after 2010 ===");
db.books.find({
  $and: [
    { in_stock: true },
    { published_year: { $gt: 2010 } }
  ]
}).pretty();

// 2. Use projection to return only title, author, and price fields
console.log("=== Books with title, author, and price only ===");
db.books.find(
  {},
  { title: 1, author: 1, price: 1, _id: 0 }
).pretty();

// 3. Sort books by price (ascending)
console.log("=== Books sorted by price (ascending) ===");
db.books.find({}, { title: 1, price: 1, _id: 0 }).sort({ price: 1 }).pretty();

// 4. Sort books by price (descending)
console.log("=== Books sorted by price (descending) ===");
db.books.find({}, { title: 1, price: 1, _id: 0 }).sort({ price: -1 }).pretty();

// 5. Pagination - First page (5 books per page)
console.log("=== First page (5 books) ===");
db.books.find({}, { title: 1, author: 1, price: 1, _id: 0 })
  .limit(5)
  .pretty();

// 6. Pagination - Second page (skip 5, limit 5)
console.log("=== Second page (next 5 books) ===");
db.books.find({}, { title: 1, author: 1, price: 1, _id: 0 })
  .skip(5)
  .limit(5)
  .pretty();

// ========================================
// TASK 4: AGGREGATION PIPELINE
// ========================================

// 1. Calculate average price of books by genre
console.log("=== Average price by genre ===");
db.books.aggregate([
  {
    $group: {
      _id: "$genre",
      averagePrice: { $avg: "$price" },
      bookCount: { $sum: 1 }
    }
  },
  {
    $sort: { averagePrice: -1 }
  }
]);

// 2. Find the author with the most books in the collection
console.log("=== Author with most books ===");
db.books.aggregate([
  {
    $group: {
      _id: "$author",
      bookCount: { $sum: 1 },
      books: { $push: "$title" }
    }
  },
  {
    $sort: { bookCount: -1 }
  },
  {
    $limit: 1
  }
]);

// 3. Group books by publication decade and count them
console.log("=== Books grouped by publication decade ===");
db.books.aggregate([
  {
    $addFields: {
      decade: {
        $multiply: [
          { $floor: { $divide: ["$published_year", 10] } },
          10
        ]
      }
    }
  },
  {
    $group: {
      _id: "$decade",
      bookCount: { $sum: 1 },
      books: { $push: { title: "$title", year: "$published_year" } }
    }
  },
  {
    $sort: { _id: 1 }
  }
]);

// ========================================
// TASK 5: INDEXING
// ========================================

// 1. Create an index on the title field
console.log("=== Creating index on title field ===");
db.books.createIndex({ title: 1 });

// 2. Create a compound index on author and published_year
console.log("=== Creating compound index on author and published_year ===");
db.books.createIndex({ author: 1, published_year: 1 });

// 3. List all indexes
console.log("=== All indexes on books collection ===");
db.books.getIndexes();

// 4. Use explain() to demonstrate performance improvement
console.log("=== Query execution plan for title search (with index) ===");
db.books.find({ title: "1984" }).explain("executionStats");

console.log("=== Query execution plan for author and year search (with compound index) ===");
db.books.find({ author: "George Orwell", published_year: { $gte: 1940 } }).explain("executionStats");

// ========================================
// ADDITIONAL USEFUL QUERIES
// ========================================

// Count total number of books
console.log("=== Total number of books ===");
db.books.countDocuments();

// Find books with price range
console.log("=== Books priced between $10 and $15 ===");
db.books.find({
  price: { $gte: 10, $lte: 15 }
}, { title: 1, price: 1, _id: 0 }).pretty();

// Find books with more than 300 pages
console.log("=== Books with more than 300 pages ===");
db.books.find({
  pages: { $gt: 300 }
}, { title: 1, pages: 1, _id: 0 }).sort({ pages: -1 }).pretty();

// Find out-of-stock books
console.log("=== Out of stock books ===");
db.books.find({ in_stock: false }, { title: 1, author: 1, _id: 0 }).pretty();

// Text search example (requires text index)
// db.books.createIndex({ title: "text", author: "text" });
// db.books.find({ $text: { $search: "tolkien" } });

console.log("=== All queries completed successfully! ===");
